{"$schema": "../gen/schemas/desktop-schema.json", "identifier": "default", "description": "Capability for the main window", "windows": ["main"], "permissions": ["core:default", "opener:default", "dialog:allow-open", "dialog:allow-save", "fs:allow-read-file", "fs:allow-write-file", "fs:allow-read-dir", "fs:allow-exists", "fs:allow-metadata", {"identifier": "fs:scope", "allow": ["$PICTURE/*", "$PICTURE/**/*", "$HOME/Desktop/*", "$HOME/Desktop/**/*", "$HOME/Downloads/*", "$HOME/Downloads/**/*", "$DOCUMENT/*", "$DOCUMENT/**/*"]}]}